using System.Net;
using System.Net.Http.Json;
using System.Security.Claims;
using AMS.API.Controllers;
using AMS.API.DTOs;
using AMS.Core.Constants;
using AMS.Core.Entities;
using AMS.Core.Exceptions;
using AMS.Core.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;

namespace AMS.API.Tests.Controllers;

public class SupportQuestionsControllerTests
{
    private readonly Mock<ISupportQuestionService> _mockService;
    private readonly SupportQuestionsController _controller;

    public SupportQuestionsControllerTests()
    {
        _mockService = new Mock<ISupportQuestionService>();
        _controller = new SupportQuestionsController(_mockService.Object);

        // Setup controller context with claims
        var claims = new List<Claim>
        {
            new("user_id", Guid.NewGuid().ToString()),
            new("email", "<EMAIL>"),
            new("role", ApplicationConstants.Roles.User)
        };
        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);

        _controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext { User = principal }
        };
    }

    [Fact]
    public async Task GetSupportQuestions_ShouldReturnPagedResults()
    {
        // Arrange
        var filter = new SupportQuestionFilterDto { Page = 1, PageSize = 10 };
        var supportQuestions = new List<SupportQuestion>
        {
            new() { Id = Guid.NewGuid(), Name = "John", Email = "<EMAIL>", Body = "Question 1" },
            new() { Id = Guid.NewGuid(), Name = "Jane", Email = "<EMAIL>", Body = "Question 2" }
        };

        _mockService.Setup(x => x.GetAllAsync(null, null, null, 0, 10, default))
            .ReturnsAsync(supportQuestions);
        _mockService.Setup(x => x.GetCountAsync(null, null, null, default))
            .ReturnsAsync(2);

        // Act
        var result = await _controller.GetSupportQuestions(filter);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<PagedSupportQuestionDto>>();
    }

    [Fact]
    public async Task GetSupportQuestion_ShouldReturnSupportQuestion_WhenExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var supportQuestion = new SupportQuestion
        {
            Id = id,
            Name = "John Doe",
            Email = "<EMAIL>",
            Body = "Test question"
        };

        _mockService.Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync(supportQuestion);

        // Act
        var result = await _controller.GetSupportQuestion(id);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<SupportQuestionDto>>();
    }

    [Fact]
    public async Task GetSupportQuestion_ShouldReturnNotFound_WhenNotExists()
    {
        // Arrange
        var id = Guid.NewGuid();

        _mockService.Setup(x => x.GetByIdAsync(id, default))
            .ReturnsAsync((SupportQuestion?)null);

        // Act
        var result = await _controller.GetSupportQuestion(id);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = result as ObjectResult;
        objectResult!.StatusCode.Should().Be(404);
    }

    [Fact]
    public async Task CreateSupportQuestion_ShouldReturnCreated_WithValidData()
    {
        // Arrange
        var createDto = new CreateSupportQuestionDto
        {
            Name = "John Doe",
            Email = "<EMAIL>",
            Body = "I need help with my account"
        };

        var createdSupportQuestion = new SupportQuestion
        {
            Id = Guid.NewGuid(),
            Name = createDto.Name,
            Email = createDto.Email,
            Body = createDto.Body,
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.Pending
        };

        _mockService.Setup(x => x.CreateAsync(createDto.Name, createDto.Email, createDto.Body, default))
            .ReturnsAsync(createdSupportQuestion);

        // Act
        var result = await _controller.CreateSupportQuestion(createDto);

        // Assert
        result.Should().BeOfType<CreatedAtActionResult>();
        var createdResult = result as CreatedAtActionResult;
        createdResult!.ActionName.Should().Be(nameof(SupportQuestionsController.GetSupportQuestion));
        createdResult.RouteValues!["id"].Should().Be(createdSupportQuestion.Id);
    }

    [Fact]
    public async Task CreateSupportQuestion_ShouldReturnBadRequest_WithValidationException()
    {
        // Arrange
        var createDto = new CreateSupportQuestionDto
        {
            Name = "",
            Email = "<EMAIL>",
            Body = "Test body"
        };

        _mockService.Setup(x => x.CreateAsync(createDto.Name, createDto.Email, createDto.Body, default))
            .ThrowsAsync(new ValidationException("Name is required"));

        // Act
        var result = await _controller.CreateSupportQuestion(createDto);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = result as ObjectResult;
        objectResult!.StatusCode.Should().Be(400);
    }

    [Fact]
    public async Task UpdateSupportQuestionStatus_ShouldReturnOk_WhenSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var updateDto = new UpdateSupportQuestionStatusDto
        {
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress
        };

        var updatedSupportQuestion = new SupportQuestion
        {
            Id = id,
            ProcessingStatus = updateDto.ProcessingStatus
        };

        _mockService.Setup(x => x.UpdateStatusAsync(id, updateDto.ProcessingStatus, "<EMAIL>", default))
            .ReturnsAsync(updatedSupportQuestion);

        // Act
        var result = await _controller.UpdateSupportQuestionStatus(id, updateDto);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<SupportQuestionDto>>();
    }

    [Fact]
    public async Task UpdateSupportQuestionStatus_ShouldReturnNotFound_WhenNotExists()
    {
        // Arrange
        var id = Guid.NewGuid();
        var updateDto = new UpdateSupportQuestionStatusDto
        {
            ProcessingStatus = ApplicationConstants.SupportQuestionStatus.InProgress
        };

        _mockService.Setup(x => x.UpdateStatusAsync(id, updateDto.ProcessingStatus, "<EMAIL>", default))
            .ThrowsAsync(new NotFoundException("Support question not found"));

        // Act
        var result = await _controller.UpdateSupportQuestionStatus(id, updateDto);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = result as ObjectResult;
        objectResult!.StatusCode.Should().Be(404);
    }

    [Fact]
    public async Task AssignSupportQuestion_ShouldReturnOk_WhenSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var assignDto = new AssignSupportQuestionDto
        {
            AssignedToUserId = userId
        };

        var assignedSupportQuestion = new SupportQuestion
        {
            Id = id,
            AssignedToUserId = userId
        };

        _mockService.Setup(x => x.AssignToUserAsync(id, userId, "<EMAIL>", default))
            .ReturnsAsync(assignedSupportQuestion);

        // Act
        var result = await _controller.AssignSupportQuestion(id, assignDto);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<SupportQuestionDto>>();
    }

    [Fact]
    public async Task DeleteSupportQuestion_ShouldReturnOk_WhenSuccessful()
    {
        // Arrange
        var id = Guid.NewGuid();

        _mockService.Setup(x => x.DeleteAsync(id, "<EMAIL>", default))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _controller.DeleteSupportQuestion(id);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<object>>();
    }

    [Fact]
    public async Task DeleteSupportQuestion_ShouldReturnNotFound_WhenNotExists()
    {
        // Arrange
        var id = Guid.NewGuid();

        _mockService.Setup(x => x.DeleteAsync(id, "<EMAIL>", default))
            .ThrowsAsync(new NotFoundException("Support question not found"));

        // Act
        var result = await _controller.DeleteSupportQuestion(id);

        // Assert
        result.Should().BeOfType<ObjectResult>();
        var objectResult = result as ObjectResult;
        objectResult!.StatusCode.Should().Be(404);
    }

    [Fact]
    public async Task GetSupportQuestionsByStatus_ShouldReturnFilteredResults()
    {
        // Arrange
        var status = ApplicationConstants.SupportQuestionStatus.Pending;
        var supportQuestions = new List<SupportQuestion>
        {
            new() { ProcessingStatus = status },
            new() { ProcessingStatus = status }
        };

        _mockService.Setup(x => x.GetByStatusAsync(status, default))
            .ReturnsAsync(supportQuestions);

        // Act
        var result = await _controller.GetSupportQuestionsByStatus(status);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<List<SupportQuestionDto>>>();
    }

    [Fact]
    public async Task GetMySupportQuestions_ShouldReturnAssignedQuestions()
    {
        // Arrange
        var userIdClaim = _controller.User.FindFirst("user_id")?.Value;
        var userId = Guid.Parse(userIdClaim!);
        var supportQuestions = new List<SupportQuestion>
        {
            new() { AssignedToUserId = userId },
            new() { AssignedToUserId = userId }
        };

        _mockService.Setup(x => x.GetByAssignedUserAsync(userId, default))
            .ReturnsAsync(supportQuestions);

        // Act
        var result = await _controller.GetMySupportQuestions();

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<List<SupportQuestionDto>>>();
    }

    [Fact]
    public async Task GetSupportQuestionsByEmail_ShouldReturnQuestionsFromEmail()
    {
        // Arrange
        var email = "<EMAIL>";
        var supportQuestions = new List<SupportQuestion>
        {
            new() { Email = email },
            new() { Email = email }
        };

        _mockService.Setup(x => x.GetByEmailAsync(email, default))
            .ReturnsAsync(supportQuestions);

        // Act
        var result = await _controller.GetSupportQuestionsByEmail(email);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = result as OkObjectResult;
        okResult!.Value.Should().BeOfType<ApiResponse<List<SupportQuestionDto>>>();
    }
}
