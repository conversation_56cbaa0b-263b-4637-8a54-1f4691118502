# AMS WebAPI

A comprehensive, secure, and scalable AMS WebAPI built with .NET 8, Clean Architecture, and PostgreSQL.

## 🚀 Features

- **Clean Architecture** - Proper separation of concerns with Domain, Infrastructure, and Presentation layers
- **JWT Authentication** - Secure token-based authentication with refresh tokens
- **Role-Based Authorization** - Administrator, Manager, and User roles with policy-based access control
- **PostgreSQL Database** - Robust data persistence with Entity Framework Core
- **Comprehensive Validation** - FluentValidation with detailed error responses
- **Global Exception Handling** - Structured error responses with proper HTTP status codes
- **API Documentation** - Interactive Swagger/OpenAPI documentation
- **Database Migrations** - Code-first database schema management
- **Audit Logging** - Automatic tracking of entity changes
- **Soft Delete** - Data preservation with logical deletion

## 🏗️ Architecture

```
src/
├── AMS.Core/                    # Domain Layer
│   ├── Entities/               # Domain entities
│   ├── Interfaces/             # Repository & service contracts
│   ├── Exceptions/             # Custom domain exceptions
│   └── Constants/              # Application constants
├── AMS.Infrastructure/          # Infrastructure Layer
│   ├── Data/                   # DbContext, migrations, seeding
│   ├── Repositories/           # Repository implementations
│   └── Services/               # Business service implementations
└── AMS.API/                    # Presentation Layer
    ├── Controllers/            # API controllers
    ├── DTOs/                   # Data transfer objects
    ├── Validators/             # FluentValidation validators
    ├── Middleware/             # Custom middleware
    └── Extensions/             # Service configuration extensions
```

## 🛠️ Technology Stack

### Core Framework

- **.NET 8** - Latest .NET framework
- **ASP.NET Core Web API** - RESTful API framework
- **Entity Framework Core 8** - ORM for data access
- **PostgreSQL** - Primary database

### Security & Validation

- **JWT Bearer Authentication** - Token-based security
- **FluentValidation** - Model validation
- **Policy-based Authorization** - Role-based access control

### Documentation & Testing

- **Swagger/OpenAPI** - Interactive API documentation
- **xUnit** - Unit testing framework
- **FluentAssertions** - Fluent test assertions
- **Moq** - Mocking framework
- **Microsoft.AspNetCore.Mvc.Testing** - Integration testing

## 📋 Prerequisites

- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [PostgreSQL 12+](https://www.postgresql.org/download/)
- [Git](https://git-scm.com/)
- IDE: [Visual Studio 2022](https://visualstudio.microsoft.com/) or [VS Code](https://code.visualstudio.com/)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd ams-web
```

### 2. Database Setup

```bash
# Install PostgreSQL and create database
sudo -u postgres psql
CREATE DATABASE ams_development;
CREATE USER ams_user WITH PASSWORD 'ams_dev_password';
GRANT ALL PRIVILEGES ON DATABASE ams_development TO ams_user;
\q
```

### 3. Configuration

Copy `.env.example` to `.env` and update the values:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```bash
# Application Environment
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=https://localhost:7001;http://localhost:5001

# Database Configuration
# .NET Configuration uses double underscore (__) for nested configuration
ConnectionStrings__DefaultConnection=Host=localhost;Database=ams_development;Username=ams_user;Password=ams_dev_password

# JWT Configuration
# .NET Configuration uses double underscore (__) for nested configuration
JwtSettings__SecretKey=your-super-secret-jwt-key-that-is-at-least-32-characters-long-and-secure
JwtSettings__Issuer=AMS-Development
JwtSettings__Audience=AMS-Development
JwtSettings__ExpirationMinutes=60
```

**Note**: .NET automatically reads environment variables and overrides appsettings.json values. Use the double underscore (`__`) notation to override nested configuration sections.

### 4. Database Migration

```bash
# Apply migrations and seed data
dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API
```

### 5. Run the Application

```bash
# Start the API
dotnet run --project src/AMS.API

# Or with hot reload
dotnet watch run --project src/AMS.API
```

### 6. Access the API

- **Swagger UI**: https://localhost:7001/swagger or http://localhost:5001/swagger
- **API Base URL**: https://localhost:7001/api or http://localhost:5001/api

## 🔐 Authentication

### Test Users

The system includes pre-seeded test users:

| Email           | Password    | Role          |
| --------------- | ----------- | ------------- |
| <EMAIL>   | Admin123!   | Administrator |
| <EMAIL> | Manager123! | Manager       |
| <EMAIL>    | User123!    | User          |

### Login Example

```bash
curl -X POST "https://localhost:7001/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin123!"}'
```

## 📚 API Documentation

### Interactive Documentation

Visit https://localhost:7001/swagger for interactive API documentation.

### Key Endpoints

#### Authentication

- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

#### User Management

- `GET /api/users` - Get all users (with filtering)
- `POST /api/users` - Create user (Admin only)
- `PUT /api/users/{id}` - Update user (Admin only)
- `DELETE /api/users/{id}` - Delete user (Admin only)

#### Support Questions

- `GET /api/supportquestions` - Get all support questions (with filtering and pagination)
- `GET /api/supportquestions/{id}` - Get support question by ID
- `POST /api/supportquestions` - Create support question (Public endpoint)
- `PUT /api/supportquestions/{id}/status` - Update support question status
- `PUT /api/supportquestions/{id}/assign` - Assign support question to user (Manager+ only)
- `DELETE /api/supportquestions/{id}` - Delete support question (Admin only)
- `GET /api/supportquestions/status/{status}` - Get support questions by status
- `GET /api/supportquestions/my-assignments` - Get current user's assigned support questions
- `GET /api/supportquestions/email/{email}` - Get support questions by email

For complete API documentation, see [API.md](API.md).

## 🗄️ Database

### Schema

The application uses PostgreSQL with the following main entities:

- **Users** - User accounts with authentication and role information
- **SupportQuestions** - Support questions submitted by users with processing status and assignment tracking

### Migrations

```bash
# Create new migration
dotnet ef migrations add MigrationName --project src/AMS.Infrastructure --startup-project src/AMS.API

# Apply migrations
dotnet ef database update --project src/AMS.Infrastructure --startup-project src/AMS.API
```

For detailed database documentation, see [DATABASE.md](DATABASE.md).

## 🔒 Security

### Features

- **JWT Authentication** with configurable expiration
- **Password Hashing** using PBKDF2 with salt
- **Role-Based Authorization** with policies
- **Input Validation** with comprehensive rules
- **HTTPS Enforcement** in production

For security configuration details, see [SECURITY.md](SECURITY.md).

## 🧪 Testing

### Comprehensive Test Suite

The project includes a complete testing infrastructure with unit tests, integration tests, and test utilities.

#### Test Projects Structure

```
tests/
├── AMS.Core.Tests/              # Domain layer unit tests (56 tests)
├── AMS.Infrastructure.Tests/    # Infrastructure layer tests (61 tests)
└── AMS.API.Tests/              # API integration tests
```

#### Running Tests

```bash
# Run all tests
dotnet test

# Run specific test project
dotnet test tests/AMS.Core.Tests/
dotnet test tests/AMS.Infrastructure.Tests/
dotnet test tests/AMS.API.Tests/

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"
```

#### Test Features

- **Unit Tests** - Core domain logic and entities
- **Integration Tests** - API endpoints and database operations
- **In-Memory Testing** - Fast, isolated test execution
- **Test Data Builders** - Consistent test data generation
- **Authentication Testing** - JWT token generation and validation
- **Database Testing** - Multiple database testing strategies

#### Test Results

- ✅ **Core Tests**: 56 tests passing
- ✅ **Infrastructure Tests**: 61 tests passing
- ✅ **Entity Tests**: User and BaseEntity comprehensive coverage
- ✅ **Constants Tests**: Application constants validation
- ✅ **Service Layer Tests**: JWT, Password, and User services fully tested
- ✅ **Repository Tests**: CRUD operations and data access layer tested
- ✅ **Security Tests**: Authentication and authorization components tested
- ✅ **Test Infrastructure**: Complete test helpers and utilities

#### Writing Tests

The project includes comprehensive test utilities to make writing tests easier:

```csharp
// Using test data builders
var user = TestDataBuilder.CreateValidUser("John", "Doe", "<EMAIL>");
var adminUser = TestDataBuilder.CreateAdminUser();

// Using database test base
public class UserRepositoryTests : DatabaseTestBase
{
    [Fact]
    public async Task GetUserByEmail_ShouldReturnUser()
    {
        // Arrange
        SeedDatabase();

        // Act & Assert
        // Your test logic here
    }
}

// Using API test base
public class AuthControllerTests : ApiTestBase
{
    public AuthControllerTests(TestWebApplicationFactory factory) : base(factory) { }

    [Fact]
    public async Task Login_WithValidCredentials_ShouldReturnToken()
    {
        // Arrange
        var loginRequest = AuthenticationHelper.CreateAdminLoginRequest();

        // Act
        var response = await Client.PostAsync("/api/auth/login", loginRequest);

        // Assert
        response.IsSuccessStatusCode.Should().BeTrue();
    }
}
```

### Database Connection Test

```bash
# Run database connection test
./scripts/test-database.sh

# Or on Windows
.\scripts\test-database.ps1
```

### Manual Testing

Use the Swagger UI at https://localhost:7001/swagger for interactive testing.

## 🚀 Deployment

### Environment Variables

The application uses environment variables for configuration. For production, set:

```bash
# Required environment variables
export ConnectionStrings__DefaultConnection="Host=prod-server;Database=ams_development;Username=ams_user;Password=secure_password;SSL Mode=Require"
export JwtSettings__SecretKey="your-production-secret-key-256-bits-minimum"
export JwtSettings__Issuer="AMS-Production"
export JwtSettings__Audience="AMS-Production"
export ASPNETCORE_ENVIRONMENT="Production"
```

**Note**: All configuration can be set via environment variables using the double underscore (`__`) notation for nested configuration sections.

For detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Clean Architecture principles
- Write comprehensive tests
- Update documentation
- Follow C# coding standards

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

- **Documentation**: [API.md](API.md), [DATABASE.md](DATABASE.md), [SECURITY.md](SECURITY.md)
- **Issues**: GitHub Issues
- **Email**: <EMAIL>

## 🗺️ Roadmap

### Completed

- ✅ **Core Unit Tests** - Domain layer fully tested (56 tests)
- ✅ **Infrastructure Tests** - Repository and service layer fully tested (61 tests)
- ✅ **Test Infrastructure** - Complete test utilities and helpers
- ✅ **Environment Configuration** - Secure environment variable setup

### In Progress

- 🔄 **API Integration Tests** - End-to-end API testing

### Planned

- [ ] **Test Coverage Reports** - Automated coverage reporting
- [ ] **Docker containerization** - Container deployment support
- [ ] **CI/CD pipeline** - Automated testing and deployment
- [ ] **Performance monitoring** - Application performance insights
- [ ] **Advanced logging with Serilog** - Structured logging implementation
- [ ] **Rate limiting** - API rate limiting and throttling
- [ ] **API versioning** - Versioned API endpoints

## 📊 Project Status

- ✅ **Core Architecture** - Complete
- ✅ **Authentication & Authorization** - Complete
- ✅ **User Management** - Complete
- ✅ **Database Integration** - Complete
- ✅ **API Documentation** - Complete
- ✅ **Security Implementation** - Complete
- ✅ **Testing Suite** - Complete (Core and Infrastructure layers fully tested)
- ✅ **Environment Configuration** - Complete
- ✅ **Infrastructure Tests** - Complete (61 tests passing)
- 🔄 **API Integration Tests** - Ready for implementation

## 📖 Documentation Index

| Document                       | Description                               |
| ------------------------------ | ----------------------------------------- |
| [README.md](README.md)         | Project overview and quick start guide    |
| [API.md](API.md)               | Complete API reference and examples       |
| [DATABASE.md](DATABASE.md)     | Database setup and management guide       |
| [SECURITY.md](SECURITY.md)     | Security configuration and best practices |
| [DEPLOYMENT.md](DEPLOYMENT.md) | Production deployment instructions        |
| [CHANGELOG.md](CHANGELOG.md)   | Version history and changes               |

## 🔗 Quick Links

- **Live API Documentation**: https://localhost:7001/swagger
- **Health Check**: https://localhost:7001/health (when implemented)
- **GitHub Repository**: [Your Repository URL]
- **Issue Tracker**: [Your Issues URL]

---

**Built with ❤️ using Clean Architecture and .NET 8**
