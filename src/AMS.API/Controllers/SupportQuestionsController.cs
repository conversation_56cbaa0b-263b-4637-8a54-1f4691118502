using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.Core.Exceptions;
using AMS.API.DTOs;
using AMS.API.Extensions;

namespace AMS.API.Controllers;

/// <summary>
/// Controller for support question management operations
/// </summary>
/// <remarks>
/// This controller provides comprehensive support question management functionality including:
/// - Support question CRUD operations
/// - Support question search and filtering
/// - Status management
/// - Assignment to users
/// 
/// Public endpoints allow anonymous users to submit support questions.
/// Management endpoints require authentication and appropriate role-based permissions.
/// </remarks>
[ApiController]
[Route("api/[controller]")]
[Tags("Support Questions")]
public class SupportQuestionsController : BaseController
{
    private readonly ISupportQuestionService _supportQuestionService;

    public SupportQuestionsController(ISupportQuestionService supportQuestionService)
    {
        _supportQuestionService = supportQuestionService;
    }

    /// <summary>
    /// Get all support questions with optional filtering and pagination
    /// </summary>
    /// <param name="filter">Filter and pagination parameters</param>
    /// <returns>Paginated list of support questions</returns>
    [HttpGet]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestions([FromQuery] SupportQuestionFilterDto filter)
    {
        try
        {
            var skip = (filter.Page - 1) * filter.PageSize;
            var supportQuestions = await _supportQuestionService.GetAllAsync(
                filter.Status,
                filter.AssignedToUserId,
                filter.Email,
                skip,
                filter.PageSize);

            var totalCount = await _supportQuestionService.GetCountAsync(
                filter.Status,
                filter.AssignedToUserId,
                filter.Email);

            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            var result = new PagedSupportQuestionDto
            {
                Items = supportQuestionDtos,
                TotalCount = totalCount,
                Page = filter.Page,
                PageSize = filter.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / filter.PageSize),
                HasNextPage = filter.Page * filter.PageSize < totalCount,
                HasPreviousPage = filter.Page > 1
            };

            return CreateResponse(result, $"Retrieved {supportQuestionDtos.Count} support questions");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support questions: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support question by ID
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <returns>Support question details</returns>
    [HttpGet("{id:guid}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestion(Guid id)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.GetByIdAsync(id);
            if (supportQuestion == null)
            {
                return CreateErrorResponse("Support question not found", 404);
            }

            return CreateResponse(supportQuestion.ToDto(), "Support question retrieved successfully");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Create a new support question (public endpoint)
    /// </summary>
    /// <param name="createDto">Support question creation data</param>
    /// <returns>Created support question</returns>
    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> CreateSupportQuestion([FromBody] CreateSupportQuestionDto createDto)
    {
        try
        {
            var supportQuestion = await _supportQuestionService.CreateAsync(
                createDto.Name,
                createDto.Email,
                createDto.Body);

            return CreatedAtAction(
                nameof(GetSupportQuestion),
                new { id = supportQuestion.Id },
                supportQuestion.ToDto());
        }
        catch (ValidationException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error creating support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Update support question status
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="updateDto">Status update data</param>
    /// <returns>Updated support question</returns>
    [HttpPut("{id:guid}/status")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> UpdateSupportQuestionStatus(Guid id, [FromBody] UpdateSupportQuestionStatusDto updateDto)
    {
        try
        {
            var updatedBy = CurrentUserEmail ?? "System";
            var supportQuestion = await _supportQuestionService.UpdateStatusAsync(id, updateDto.ProcessingStatus, updatedBy);

            return CreateResponse(supportQuestion.ToDto(), "Support question status updated successfully");
        }
        catch (NotFoundException ex)
        {
            return CreateErrorResponse(ex.Message, 404);
        }
        catch (ValidationException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error updating support question status: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Assign support question to a user
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <param name="assignDto">Assignment data</param>
    /// <returns>Updated support question</returns>
    [HttpPut("{id:guid}/assign")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireManagerRole)]
    public async Task<IActionResult> AssignSupportQuestion(Guid id, [FromBody] AssignSupportQuestionDto assignDto)
    {
        try
        {
            var updatedBy = CurrentUserEmail ?? "System";
            var supportQuestion = await _supportQuestionService.AssignToUserAsync(id, assignDto.AssignedToUserId, updatedBy);

            return CreateResponse(supportQuestion.ToDto(), "Support question assigned successfully");
        }
        catch (NotFoundException ex)
        {
            return CreateErrorResponse(ex.Message, 404);
        }
        catch (ValidationException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error assigning support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Delete support question
    /// </summary>
    /// <param name="id">Support question ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id:guid}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> DeleteSupportQuestion(Guid id)
    {
        try
        {
            var deletedBy = CurrentUserEmail ?? "System";
            await _supportQuestionService.DeleteAsync(id, deletedBy);

            return CreateResponse<object>(null, "Support question deleted successfully");
        }
        catch (NotFoundException ex)
        {
            return CreateErrorResponse(ex.Message, 404);
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error deleting support question: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support questions by status
    /// </summary>
    /// <param name="status">Processing status</param>
    /// <returns>List of support questions with the specified status</returns>
    [HttpGet("status/{status}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestionsByStatus(string status)
    {
        try
        {
            var supportQuestions = await _supportQuestionService.GetByStatusAsync(status);
            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            return CreateResponse(supportQuestionDtos, $"Retrieved {supportQuestionDtos.Count} support questions with status '{status}'");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support questions by status: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support questions assigned to current user
    /// </summary>
    /// <returns>List of support questions assigned to the current user</returns>
    [HttpGet("my-assignments")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetMySupportQuestions()
    {
        try
        {
            if (!Guid.TryParse(CurrentUserId, out var userId))
            {
                return CreateErrorResponse("Invalid user ID", 400);
            }

            var supportQuestions = await _supportQuestionService.GetByAssignedUserAsync(userId);
            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            return CreateResponse(supportQuestionDtos, $"Retrieved {supportQuestionDtos.Count} assigned support questions");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving assigned support questions: {ex.Message}", 500);
        }
    }

    /// <summary>
    /// Get support questions by email address
    /// </summary>
    /// <param name="email">Email address</param>
    /// <returns>List of support questions from the specified email</returns>
    [HttpGet("email/{email}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetSupportQuestionsByEmail(string email)
    {
        try
        {
            var supportQuestions = await _supportQuestionService.GetByEmailAsync(email);
            var supportQuestionDtos = supportQuestions.Select(sq => sq.ToDto()).ToList();

            return CreateResponse(supportQuestionDtos, $"Retrieved {supportQuestionDtos.Count} support questions from email '{email}'");
        }
        catch (Exception ex)
        {
            return CreateErrorResponse($"Error retrieving support questions by email: {ex.Message}", 500);
        }
    }
}
