using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AMS.Core.Interfaces;
using AMS.Core.Constants;
using AMS.Core.Exceptions;
using AMS.API.DTOs;
using AMS.API.Extensions;

namespace AMS.API.Controllers;

/// <summary>
/// Controller for user management operations
/// </summary>
/// <remarks>
/// This controller provides comprehensive user management functionality including:
/// - User CRUD operations
/// - User search and filtering
/// - Password management
/// - Account activation/deactivation
///
/// Most endpoints require authentication and appropriate role-based permissions.
/// </remarks>
[ApiController]
[Route("api/[controller]")]
[Tags("Users")]
public class UsersController : BaseController
{
    private readonly IUserService _userService;

    public UsersController(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// Get all users
    /// </summary>
    /// <param name="role">Filter by role (optional)</param>
    /// <param name="isActive">Filter by active status (optional)</param>
    /// <param name="search">Search term (optional)</param>
    /// <returns>List of users</returns>
    [HttpGet]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetUsers(
        [FromQuery] string? role = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] string? search = null)
    {
        IEnumerable<Core.Entities.User> users;

        if (!string.IsNullOrEmpty(search))
        {
            users = await _userService.SearchUsersAsync(search);
        }
        else if (!string.IsNullOrEmpty(role))
        {
            users = await _userService.GetUsersByRoleAsync(role);
        }
        else if (isActive.HasValue && isActive.Value)
        {
            users = await _userService.GetActiveUsersAsync();
        }
        else
        {
            users = await _userService.GetAllUsersAsync();
        }

        // Apply additional filters if needed
        if (isActive.HasValue && !string.IsNullOrEmpty(search))
        {
            users = users.Where(u => u.IsActive == isActive.Value);
        }

        return CreateResponse(users.ToDto(), "Users retrieved successfully");
    }

    /// <summary>
    /// Get user by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User details</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireUserRole)]
    public async Task<IActionResult> GetUser(Guid id)
    {
        var user = await _userService.GetUserByIdAsync(id);
        if (user == null)
        {
            return CreateErrorResponse("User not found", 404);
        }

        return CreateResponse(user.ToDto(), "User retrieved successfully");
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    /// <param name="createUserDto">User creation data</param>
    /// <returns>Created user</returns>
    [HttpPost]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> CreateUser([FromBody] CreateUserDto createUserDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var user = createUserDto.ToEntity();
            user.CreatedBy = CurrentUserEmail;

            var createdUser = await _userService.CreateUserAsync(user);
            return CreatedAtAction(nameof(GetUser), new { id = createdUser.Id }, createdUser.ToDto());
        }
        catch (DuplicateEmailException ex)
        {
            return CreateErrorResponse(ex.Message, 409);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to create user", 500);
        }
    }

    /// <summary>
    /// Update user information
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="updateUserDto">User update data</param>
    /// <returns>Updated user</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UpdateUserDto updateUserDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var user = updateUserDto.ToEntity(id);
            user.UpdatedBy = CurrentUserEmail;

            var updatedUser = await _userService.UpdateUserAsync(user);
            return CreateResponse(updatedUser.ToDto(), "User updated successfully");
        }
        catch (EntityNotFoundException)
        {
            return CreateErrorResponse("User not found", 404);
        }
        catch (DuplicateEmailException ex)
        {
            return CreateErrorResponse(ex.Message, 409);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to update user", 500);
        }
    }

    /// <summary>
    /// Delete user (soft delete)
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        try
        {
            await _userService.DeleteUserAsync(id);
            return CreateResponse<object>(null, "User deleted successfully");
        }
        catch (EntityNotFoundException)
        {
            return CreateErrorResponse("User not found", 404);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to delete user", 500);
        }
    }

    /// <summary>
    /// Deactivate user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/deactivate")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> DeactivateUser(Guid id)
    {
        var result = await _userService.DeactivateUserAsync(id);
        if (!result)
        {
            return CreateErrorResponse("User not found", 404);
        }

        return CreateResponse<object>(null, "User deactivated successfully");
    }

    /// <summary>
    /// Activate user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/activate")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> ActivateUser(Guid id)
    {
        var result = await _userService.ActivateUserAsync(id);
        if (!result)
        {
            return CreateErrorResponse("User not found", 404);
        }

        return CreateResponse<object>(null, "User activated successfully");
    }

    /// <summary>
    /// Change user password
    /// </summary>
    /// <param name="changePasswordDto">Password change data</param>
    /// <returns>Success response</returns>
    [HttpPost("change-password")]
    [Authorize]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        if (CurrentUserId == null || !Guid.TryParse(CurrentUserId, out var userId))
        {
            return CreateErrorResponse("User not found", 404);
        }

        try
        {
            var result = await _userService.ChangePasswordAsync(userId, changePasswordDto.CurrentPassword, changePasswordDto.NewPassword);
            if (!result)
            {
                return CreateErrorResponse("Current password is incorrect", 400);
            }

            return CreateResponse<object>(null, "Password changed successfully");
        }
        catch (InvalidPasswordException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to change password", 500);
        }
    }

    /// <summary>
    /// Reset user password (admin only)
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="resetPasswordDto">Password reset data</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/reset-password")]
    [Authorize(Policy = ApplicationConstants.Policies.RequireAdministratorRole)]
    public async Task<IActionResult> ResetPassword(Guid id, [FromBody] ResetPasswordDto resetPasswordDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var result = await _userService.ResetPasswordAsync(id, resetPasswordDto.NewPassword);
            if (!result)
            {
                return CreateErrorResponse("User not found", 404);
            }

            return CreateResponse<object>(null, "Password reset successfully");
        }
        catch (InvalidPasswordException ex)
        {
            return CreateErrorResponse(ex.Message, 400);
        }
        catch (Exception)
        {
            return CreateErrorResponse("Failed to reset password", 500);
        }
    }
}
