@model AMS.Core.Entities.User
@{
    ViewData["Title"] = "User Details";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">User Details</h1>
        <p class="text-muted mb-0">View user information and account details</p>
    </div>
    <div>
        <a href="/users" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Users
        </a>
    </div>
</div>

<div class="row g-4">
    <!-- User Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person me-2"></i>
                    User Information
                </h5>
                <span class="badge bg-@(Model.IsActive ? "success" : "secondary")">
                    @(Model.IsActive ? "Active" : "Inactive")
                </span>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label text-muted">First Name</label>
                        <p class="form-control-plaintext">@Model.FirstName</p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">Last Name</label>
                        <p class="form-control-plaintext">@Model.LastName</p>
                    </div>
                    <div class="col-12">
                        <label class="form-label text-muted">Email Address</label>
                        <p class="form-control-plaintext">
                            <i class="bi bi-envelope me-2"></i>
                            <a href="mailto:@Model.Email">@Model.Email</a>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">Role</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-@(Model.Role == "Administrator" ? "danger" : Model.Role == "Manager" ? "warning" : "info")">
                                @Model.Role
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label text-muted">Status</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-@(Model.IsActive ? "success" : "secondary")">
                                @(Model.IsActive ? "Active" : "Inactive")
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Account Information
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <i class="bi bi-calendar-plus text-primary fs-4"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-0">Created</h6>
                        <small class="text-muted">@Model.CreatedAt.ToString("MMMM dd, yyyy")</small>
                    </div>
                </div>
                
                @if (Model.UpdatedAt.HasValue)
                {
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <i class="bi bi-pencil text-info fs-4"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Last Updated</h6>
                            <small class="text-muted">@Model.UpdatedAt.Value.ToString("MMM dd, yyyy HH:mm")</small>
                        </div>
                    </div>
                }
                
                @if (Model.LastLoginAt.HasValue)
                {
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-clock-history text-success fs-4"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Last Login</h6>
                            <small class="text-muted">@Model.LastLoginAt.Value.ToString("MMM dd, yyyy HH:mm")</small>
                        </div>
                    </div>
                }
                else
                {
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-clock text-muted fs-4"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">Last Login</h6>
                            <small class="text-muted">Never</small>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/users/edit/@Model.Id" class="btn btn-primary btn-sm">
                        <i class="bi bi-pencil me-2"></i>
                        Edit User
                    </a>
                    
                    <a href="mailto:@Model.Email" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-envelope me-2"></i>
                        Send Email
                    </a>
                    
                    @if (User.FindFirst("user_id")?.Value != Model.Id.ToString())
                    {
                        @if (Model.IsActive)
                        {
                            <button class="btn btn-outline-warning btn-sm" disabled>
                                <i class="bi bi-pause-circle me-2"></i>
                                Deactivate User
                            </button>
                        }
                        else
                        {
                            <button class="btn btn-outline-success btn-sm" disabled>
                                <i class="bi bi-play-circle me-2"></i>
                                Activate User
                            </button>
                        }
                        
                        <a href="/users/delete/@Model.Id" class="btn btn-outline-danger btn-sm">
                            <i class="bi bi-trash me-2"></i>
                            Delete User
                        </a>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <small>
                                <i class="bi bi-info-circle me-1"></i>
                                You cannot modify your own account.
                            </small>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
