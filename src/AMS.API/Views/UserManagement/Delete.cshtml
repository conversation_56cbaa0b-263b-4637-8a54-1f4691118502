@model AMS.Core.Entities.User
@{
    ViewData["Title"] = "Delete User";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 text-danger">Delete User</h1>
        <p class="text-muted mb-0">Permanently remove user from the system</p>
    </div>
    <div>
        <a href="/users" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to Users
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card border-danger">
            <div class="card-header bg-danger bg-opacity-10">
                <h5 class="card-title mb-0 text-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Confirm Deletion
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <strong>Warning:</strong> This action cannot be undone. All data associated with this user will be permanently deleted.
                </div>

                <div class="user-info mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-circle bg-danger text-white me-3">
                            @Model.FirstName.Substring(0, 1).ToUpper()@Model.LastName.Substring(0, 1).ToUpper()
                        </div>
                        <div>
                            <h6 class="mb-0">@Model.FullName</h6>
                            <small class="text-muted">@Model.Email</small>
                        </div>
                    </div>
                    
                    <div class="row g-2">
                        <div class="col-6">
                            <strong>Role:</strong>
                            <span class="badge bg-@(Model.Role == "Administrator" ? "danger" : Model.Role == "Manager" ? "warning" : "info")">
                                @Model.Role
                            </span>
                        </div>
                        <div class="col-6">
                            <strong>Status:</strong>
                            <span class="badge bg-@(Model.IsActive ? "success" : "secondary")">
                                @(Model.IsActive ? "Active" : "Inactive")
                            </span>
                        </div>
                        <div class="col-6">
                            <strong>Created:</strong>
                            <small>@Model.CreatedAt.ToString("MMM dd, yyyy")</small>
                        </div>
                        <div class="col-6">
                            <strong>Last Login:</strong>
                            <small>@(Model.LastLoginAt?.ToString("MMM dd, yyyy") ?? "Never")</small>
                        </div>
                    </div>
                </div>

                <div class="deletion-impact mb-4">
                    <h6 class="text-danger">What will be deleted:</h6>
                    <ul class="text-muted">
                        <li>User account and profile information</li>
                        <li>Login credentials and access permissions</li>
                        <li>User activity logs and audit trail</li>
                        <li>Any assigned support questions will be unassigned</li>
                    </ul>
                </div>

                <form asp-action="Delete" method="post">
                    <input type="hidden" asp-for="Id" />
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                        <label class="form-check-label" for="confirmDelete">
                            I understand that this action cannot be undone and will permanently delete <strong>@Model.FullName</strong> from the system.
                        </label>
                    </div>

                    <div class="d-flex justify-content-end gap-2">
                        <a href="/users/details/@Model.Id" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                            <i class="bi bi-trash me-2"></i>
                            Delete User
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    Alternative Actions
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-3">Instead of deleting, consider these alternatives:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-warning btn-sm" disabled>
                        <i class="bi bi-pause-circle me-2"></i>
                        Deactivate User (Preserves data)
                    </button>
                    <a href="/users/edit/@Model.Id" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-pencil me-2"></i>
                        Edit User Information
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const confirmCheckbox = document.getElementById('confirmDelete');
            const deleteButton = document.getElementById('deleteButton');
            
            confirmCheckbox.addEventListener('change', function() {
                deleteButton.disabled = !this.checked;
            });
        });
    </script>
}

<style>
.avatar-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
}
</style>
