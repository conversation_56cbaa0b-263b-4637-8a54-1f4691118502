@model UserListViewModel
@{
    ViewData["Title"] = "User Management";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">User Management</h1>
        <p class="text-muted mb-0">Manage system users and their permissions</p>
    </div>
    <div>
        <a asp-action="Create" class="btn btn-primary">
            <i class="bi bi-person-plus me-2"></i>
            Add New User
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" name="search" id="search" class="form-control" value="@Model.SearchTerm" placeholder="Search by name or email...">
            </div>
            <div class="col-md-3">
                <label for="role" class="form-label">Role</label>
                <select name="role" id="role" class="form-select">
                    <option value="">All Roles</option>
                    <option value="Administrator" selected="@(Model.RoleFilter == "Administrator")">Administrator</option>
                    <option value="Manager" selected="@(Model.RoleFilter == "Manager")">Manager</option>
                    <option value="User" selected="@(Model.RoleFilter == "User")">User</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="isActive" class="form-label">Status</label>
                <select name="isActive" id="isActive" class="form-select">
                    <option value="">All</option>
                    <option value="true" selected="@(Model.IsActiveFilter == true)">Active</option>
                    <option value="false" selected="@(Model.IsActiveFilter == false)">Inactive</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-funnel me-1"></i>
                    Filter
                </button>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-1"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Statistics -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">@Model.Users.Count</h5>
                <p class="card-text text-muted">Total Users</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">@Model.Users.Count(u => u.IsActive)</h5>
                <p class="card-text text-muted">Active</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">@Model.Users.Count(u => u.Role == "Administrator")</h5>
                <p class="card-text text-muted">Administrators</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">@Model.Users.Count(u => u.Role == "Manager")</h5>
                <p class="card-text text-muted">Managers</p>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-body">
        @if (Model.Users.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.Users)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-primary text-white me-3">
                                            @user.FirstName.Substring(0, 1).ToUpper()@user.LastName.Substring(0, 1).ToUpper()
                                        </div>
                                        <div>
                                            <strong>@user.FullName</strong>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <a href="mailto:@user.Email" class="text-decoration-none">
                                        @user.Email
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-@(user.Role == "Administrator" ? "danger" : user.Role == "Manager" ? "warning" : "info")">
                                        @user.Role
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-@(user.IsActive ? "success" : "secondary")">
                                        @(user.IsActive ? "Active" : "Inactive")
                                    </span>
                                </td>
                                <td>
                                    @if (user.LastLoginAt.HasValue)
                                    {
                                        <small class="text-muted">
                                            @user.LastLoginAt.Value.ToString("MMM dd, yyyy")
                                            <br>
                                            @user.LastLoginAt.Value.ToString("HH:mm")
                                        </small>
                                    }
                                    else
                                    {
                                        <small class="text-muted">Never</small>
                                    }
                                </td>
                                <td>
                                    <small class="text-muted">
                                        @user.CreatedAt.ToString("MMM dd, yyyy")
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@user.Id" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@user.Id" 
                                           class="btn btn-sm btn-outline-secondary" title="Edit User">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        @if (User.FindFirst("user_id")?.Value != user.Id.ToString())
                                        {
                                            <a asp-action="Delete" asp-route-id="@user.Id" 
                                               class="btn btn-sm btn-outline-danger" title="Delete User"
                                               data-confirm-delete="Are you sure you want to delete @user.FullName?">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
                <h5 class="mt-3 text-muted">No users found</h5>
                <p class="text-muted">
                    @if (!string.IsNullOrEmpty(Model.SearchTerm) || !string.IsNullOrEmpty(Model.RoleFilter) || Model.IsActiveFilter.HasValue)
                    {
                        <span>Try adjusting your filters or </span>
                    }
                    <a asp-action="Create">add a new user</a>.
                </p>
            </div>
        }
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.875rem;
}
</style>
