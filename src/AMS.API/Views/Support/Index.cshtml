@model SupportQuestionListViewModel
@{
    ViewData["Title"] = "Support Questions";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1">Support Questions</h1>
        <p class="text-muted mb-0">Manage and respond to support questions</p>
    </div>
    <div>
        <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            New Question
        </a>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="Pending" selected="@(Model.StatusFilter == "Pending")">Pending</option>
                    <option value="In Progress" selected="@(Model.StatusFilter == "In Progress")">In Progress</option>
                    <option value="Resolved" selected="@(Model.StatusFilter == "Resolved")">Resolved</option>
                    <option value="Closed" selected="@(Model.StatusFilter == "Closed")">Closed</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="email" class="form-label">Email</label>
                <input type="email" name="email" id="email" class="form-control" value="@Model.EmailFilter" placeholder="Filter by email...">
            </div>
            <div class="col-md-2">
                <label for="pageSize" class="form-label">Per Page</label>
                <select name="pageSize" id="pageSize" class="form-select">
                    <option value="10" selected="@(Model.PageSize == 10)">10</option>
                    <option value="25" selected="@(Model.PageSize == 25)">25</option>
                    <option value="50" selected="@(Model.PageSize == 50)">50</option>
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="bi bi-funnel me-1"></i>
                    Filter
                </button>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-1"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Statistics -->
<div class="row g-3 mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">@Model.TotalCount</h5>
                <p class="card-text text-muted">Total Questions</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">@Model.SupportQuestions.Count(q => q.ProcessingStatus == "Pending")</h5>
                <p class="card-text text-muted">Pending</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">@Model.SupportQuestions.Count(q => q.ProcessingStatus == "In Progress")</h5>
                <p class="card-text text-muted">In Progress</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">@Model.SupportQuestions.Count(q => q.ProcessingStatus == "Resolved")</h5>
                <p class="card-text text-muted">Resolved</p>
            </div>
        </div>
    </div>
</div>

<!-- Support Questions Table -->
<div class="card">
    <div class="card-body">
        @if (Model.SupportQuestions.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Question</th>
                            <th>Status</th>
                            <th>Assigned To</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var question in Model.SupportQuestions)
                        {
                            <tr>
                                <td>
                                    <strong>@question.Name</strong>
                                </td>
                                <td>
                                    <a href="mailto:@question.Email" class="text-decoration-none">
                                        @question.Email
                                    </a>
                                </td>
                                <td>
                                    <div class="text-truncate-2" style="max-width: 200px;" title="@question.Body">
                                        @question.Body
                                    </div>
                                </td>
                                <td>
                                    <span class="badge status-badge <EMAIL>().Replace(" ", "-")">
                                        @question.ProcessingStatus
                                    </span>
                                </td>
                                <td>
                                    @if (question.AssignedToUserId.HasValue)
                                    {
                                        <span class="text-success">
                                            <i class="bi bi-person-check me-1"></i>
                                            Assigned
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="text-muted">
                                            <i class="bi bi-person-dash me-1"></i>
                                            Unassigned
                                        </span>
                                    }
                                </td>
                                <td>
                                    <small class="text-muted">
                                        @question.CreatedAt.ToString("MMM dd, yyyy")
                                        <br>
                                        @question.CreatedAt.ToString("HH:mm")
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a asp-action="Details" asp-route-id="@question.Id" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        @if (User.IsInRole("Administrator") || User.IsInRole("Manager"))
                                        {
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                        data-bs-toggle="dropdown" title="Quick Actions">
                                                    <i class="bi bi-gear"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <form asp-action="UpdateStatus" method="post" class="d-inline">
                                                            <input type="hidden" name="id" value="@question.Id" />
                                                            <input type="hidden" name="status" value="In Progress" />
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="bi bi-arrow-clockwise me-2"></i>Mark In Progress
                                                            </button>
                                                        </form>
                                                    </li>
                                                    <li>
                                                        <form asp-action="UpdateStatus" method="post" class="d-inline">
                                                            <input type="hidden" name="id" value="@question.Id" />
                                                            <input type="hidden" name="status" value="Resolved" />
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="bi bi-check-circle me-2"></i>Mark Resolved
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if (Model.TotalPages > 1)
            {
                <nav aria-label="Support questions pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item @(Model.HasPreviousPage ? "" : "disabled")">
                            <a class="page-link" asp-route-page="@(Model.CurrentPage - 1)" asp-route-status="@Model.StatusFilter" asp-route-email="@Model.EmailFilter" asp-route-pageSize="@Model.PageSize">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        
                        @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                <a class="page-link" asp-route-page="@i" asp-route-status="@Model.StatusFilter" asp-route-email="@Model.EmailFilter" asp-route-pageSize="@Model.PageSize">@i</a>
                            </li>
                        }
                        
                        <li class="page-item @(Model.HasNextPage ? "" : "disabled")">
                            <a class="page-link" asp-route-page="@(Model.CurrentPage + 1)" asp-route-status="@Model.StatusFilter" asp-route-email="@Model.EmailFilter" asp-route-pageSize="@Model.PageSize">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            }
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-chat-dots text-muted" style="font-size: 4rem;"></i>
                <h5 class="mt-3 text-muted">No support questions found</h5>
                <p class="text-muted">
                    @if (!string.IsNullOrEmpty(Model.StatusFilter) || !string.IsNullOrEmpty(Model.EmailFilter))
                    {
                        <span>Try adjusting your filters or </span>
                    }
                    <a asp-controller="SupportQuestions" asp-action="Create">submit a new question</a>.
                </p>
            </div>
        }
    </div>
</div>
