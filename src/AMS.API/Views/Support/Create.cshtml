@model CreateSupportQuestionViewModel
@{
    ViewData["Title"] = "Submit Support Question";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-5">
                <i class="bi bi-question-circle text-primary" style="font-size: 4rem;"></i>
                <h1 class="h3 mt-3 mb-2">Submit a Support Question</h1>
                <p class="text-muted">
                    Need help? We're here to assist you. Fill out the form below and our team will get back to you as soon as possible.
                </p>
            </div>

            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form asp-action="Create" method="post" data-loading="true">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input asp-for="Name" class="form-control" placeholder="Your Name" />
                                    <label asp-for="Name"></label>
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                                    <label asp-for="Email"></label>
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-floating">
                                    <textarea asp-for="Body" class="form-control" placeholder="Describe your question or issue..." style="height: 150px;"></textarea>
                                    <label asp-for="Body"></label>
                                    <span asp-validation-for="Body" class="text-danger"></span>
                                </div>
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>
                                    Please provide as much detail as possible to help us assist you better.
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                <small>
                                    <i class="bi bi-shield-check me-1"></i>
                                    Your information is secure and will only be used to respond to your question.
                                </small>
                            </div>
                            <div>
                                <a asp-controller="Home" asp-action="Index" class="btn btn-outline-secondary me-2">
                                    <i class="bi bi-arrow-left me-1"></i>
                                    Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-send me-2"></i>
                                    Submit Question
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Information -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <i class="bi bi-clock text-info fs-3 mb-2"></i>
                            <h6 class="card-title">Response Time</h6>
                            <p class="card-text text-muted small">
                                We typically respond to support questions within 24 hours during business days.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <i class="bi bi-people text-success fs-3 mb-2"></i>
                            <h6 class="card-title">Expert Support</h6>
                            <p class="card-text text-muted small">
                                Our experienced support team is ready to help you with any questions or issues.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-question-circle me-2"></i>
                        Frequently Asked Questions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    How do I reset my password?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Contact your system administrator to reset your password. For security reasons, password resets must be handled by authorized personnel.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    How do I access different features?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Feature access depends on your user role. Administrators have full access, Managers can handle support questions, and Users have basic access. Contact your administrator if you need additional permissions.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    What information should I include in my question?
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Please include as much detail as possible: what you were trying to do, what happened, any error messages you saw, and steps to reproduce the issue. This helps us provide better assistance.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
