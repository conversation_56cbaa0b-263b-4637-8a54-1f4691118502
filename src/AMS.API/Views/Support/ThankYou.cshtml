@model AMS.Core.Entities.SupportQuestion
@{
    ViewData["Title"] = "Thank You";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="text-center">
                <div class="success-icon mb-4">
                    <i class="bi bi-check-circle text-success" style="font-size: 5rem;"></i>
                </div>
                
                <h1 class="h3 text-success mb-3">Thank You!</h1>
                <h2 class="h5 mb-4">Your support question has been submitted successfully</h2>
                
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Question Details</h6>
                        <div class="row text-start">
                            <div class="col-sm-4">
                                <strong>Reference ID:</strong>
                            </div>
                            <div class="col-sm-8">
                                <code>@Model.Id.ToString().Substring(0, 8).ToUpper()</code>
                            </div>
                        </div>
                        <div class="row text-start mt-2">
                            <div class="col-sm-4">
                                <strong>Submitted:</strong>
                            </div>
                            <div class="col-sm-8">
                                @Model.CreatedAt.ToString("MMMM dd, yyyy 'at' HH:mm")
                            </div>
                        </div>
                        <div class="row text-start mt-2">
                            <div class="col-sm-4">
                                <strong>Status:</strong>
                            </div>
                            <div class="col-sm-8">
                                <span class="badge bg-warning">@Model.ProcessingStatus</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <div class="d-flex align-items-start">
                        <i class="bi bi-info-circle me-3 fs-5 mt-1"></i>
                        <div class="text-start">
                            <strong>What happens next?</strong><br>
                            <ul class="mb-0 mt-2">
                                <li>Our support team will review your question</li>
                                <li>You'll receive a response via email within 24 hours</li>
                                <li>Keep your reference ID for future correspondence</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex gap-3 justify-content-center mt-4">
                    <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                        <i class="bi bi-house me-2"></i>
                        Go Home
                    </a>
                    <a asp-controller="SupportQuestions" asp-action="Create" class="btn btn-outline-secondary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Submit Another Question
                    </a>
                </div>
                
                <hr class="my-4">
                
                <div class="text-muted">
                    <h6>Need immediate assistance?</h6>
                    <p class="mb-0">
                        For urgent issues, please contact your system administrator directly.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>


